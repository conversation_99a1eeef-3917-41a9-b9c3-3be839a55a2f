using VNR.Core.Domain.Entities.Evaluation;

namespace VNR.Service.Evaluation.Application.Evaluation.Eva_Checkin.Services
{
    public interface IEva_CheckinService
    {
        Task<IEnumerable<Eva_Checkin>> GetAllAsync();
        Task<Eva_Checkin?> GetByIdAsync(Guid id);
        Task<Guid> CreateAsync(Eva_Checkin entity);
        Task<bool> UpdateAsync(Eva_Checkin entity);
        Task<bool> DeleteAsync(Guid id);
    }
}