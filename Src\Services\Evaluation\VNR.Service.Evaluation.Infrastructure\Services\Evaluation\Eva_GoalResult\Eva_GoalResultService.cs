using Microsoft.EntityFrameworkCore;
using VNR.Core.Domain.Entities.Evaluation;
using VNR.Infrastructure.BaseRepositories.Repositories;
using VNR.Infrastructure.BaseRepositories.Services.ApplicationContext;
using VNR.Service.Evaluation.Application.Evaluation.Eva_GoalResult.Services;

namespace VNR.Service.Evaluation.Infrastructure.Services.Evaluation.Eva_GoalResult;

public class Eva_GoalResultService : IEva_GoalResultService
{
    private readonly IGenericRepository<Eva_GoalResult, Guid> _repository;
    private readonly ILoggingService _loggingService;

    public Eva_GoalResultService(IGenericRepository<Eva_GoalResult, Guid> repository, ILoggingService loggingService)
    {
        _repository = repository;
        _loggingService = loggingService;
    }

    public async Task<IEnumerable<Eva_GoalResult>> GetAllAsync()
    {
        await _loggingService.LogInformationAsync("Getting all Eva_GoalResult entities");
        var query = await _repository.GetAllAsync();
        return await query.ToListAsync();
    }

    public async Task<Eva_GoalResult?> GetByIdAsync(Guid id)
    {
        await _loggingService.LogInformationAsync($"Getting Eva_GoalResult by id {id}");
        return await _repository.GetByIdAsync(id);
    }

    public async Task<Guid> CreateAsync(Eva_GoalResult entity)
    {
        await _repository.AddAsync(entity);
        await _loggingService.LogInformationAsync($"Created Eva_GoalResult with id {entity.Id}");
        return entity.Id;
    }

    public async Task<bool> UpdateAsync(Eva_GoalResult entity)
    {
        await _repository.UpdateAsync(entity);
        await _loggingService.LogInformationAsync($"Updated Eva_GoalResult with id {entity.Id}");
        return true;
    }

    public async Task<bool> DeleteAsync(Guid id)
    {
        var result = await _repository.DeleteAsync(id);
        await _loggingService.LogInformationAsync($"Deleted Eva_GoalResult with id {id}");
        return result;
    }
}
