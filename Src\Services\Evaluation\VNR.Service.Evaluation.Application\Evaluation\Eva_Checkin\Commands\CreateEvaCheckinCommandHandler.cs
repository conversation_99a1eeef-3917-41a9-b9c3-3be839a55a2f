using VNR.Core.Application.Command;
using VNR.Core.Models;
using VNR.Infrastructure.BaseRepositories.Services.ApplicationContext;
using VNR.Service.Evaluation.Application.Evaluation.Services;

namespace VNR.Service.Evaluation.Application.Evaluation.Eva_Checkin.Commands;

public class CreateEvaCheckinCommandHandler : CommandHandler<CreateEvaCheckinCommand, Guid>
{
    private readonly IApplicationContext _applicationContext;
    private readonly IEva_CheckinService _service;

    public CreateEvaCheckinCommandHandler(IEva_CheckinService service, IApplicationContext applicationContext)
        : base(applicationContext.Accessor)
    {
        (_applicationContext, _service) = (applicationContext, service);
    }

    public override async Task<IApiResult<Guid>> Handle(CreateEvaCheckinCommand request,
        CancellationToken cancellationToken)
    {
        var entity = _applicationContext.Mapper.Map<Core.Domain.Entities.Evaluation.Eva_Checkin>(request.Request);
        var id = await _service.CreateAsync(entity);
        return Result(id);
    }
}