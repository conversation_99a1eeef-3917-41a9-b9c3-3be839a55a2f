using Microsoft.AspNetCore.Mvc;
using NSwag.Annotations;
using VNR.Core.Api;
using VNR.Core.Api.Services;
using VNR.Service.Evaluation.Application.Evaluation.Commands;
using VNR.Service.Evaluation.Application.Evaluation.Queries;

namespace VNR.Service.Evaluation.Api.Controllers.Evaluation;

/// <summary>
/// Eva Goal Detail Controller
/// </summary>
[OpenApiTag("[Evaluation][Eva_GoalDetail]", Description = "APIs chi tiết mục tiêu")]
[ApiVersion("1")]
[Route("api/v{version:apiVersion}/[controller]")]
public class Eva_GoalDetailController : BaseApiController
{
    /// <summary>
    /// Constructor
    /// </summary>
    public Eva_GoalDetailController(IApiContext context) : base(context)
    { }

    /// <summary>
    /// L<PERSON>y danh sách Eva Goal Details
    /// </summary>
    /// <param name="query">Thông tin lọc</param>
    /// <returns><PERSON>h sách <PERSON> Goal Details</returns>
    [HttpGet]
    public async Task<IActionResult> List(ListEvaGoalDetailQuery query)
        => await HandleRequest(query);

    /// <summary>
    /// Lấy thông tin Eva Goal Detail theo Id
    /// </summary>
    [HttpGet("{ID:guid}")]
    public async Task<IActionResult> GetById(GetEvaGoalDetailByIdQuery query)
        => await HandleRequest(query);

    /// <summary>
    /// Tạo mới Eva Goal Detail
    /// </summary>
    /// <param name="command">Thông tin Eva Goal Detail</param>
    /// <returns>Id của Eva Goal Detail mới</returns>
    [HttpPost]
    public async Task<IActionResult> Create(CreateEvaGoalDetailCommand command)
        => await HandleRequest(command);

    /// <summary>
    /// Cập nhật Eva Goal Detail
    /// </summary>
    [HttpPut("{ID:guid}")]
    public async Task<IActionResult> Update(UpdateEvaGoalDetailCommand command)
        => await HandleRequest(command);

    /// <summary>
    /// Xóa Eva Goal Detail
    /// </summary>
    [HttpDelete("{ID:guid}")]
    public async Task<IActionResult> Delete(DeleteEvaGoalDetailCommand command)
        => await HandleRequest(command);
}