using VNR.Core.Application.Command;
using VNR.Core.Domain.Entities.Evaluation;
using VNR.Core.Models;
using VNR.Infrastructure.BaseRepositories.Services.ApplicationContext;
using VNR.Service.Evaluation.Application.Evaluation.Eva_GoalProgressHistory.Services;

namespace VNR.Service.Evaluation.Application.Evaluation.Eva_GoalProgressHistory.Commands;

public class CreateEvaGoalProgressHistoryCommandHandler : CommandHandler<CreateEvaGoalProgressHistoryCommand, Guid>
{
    private readonly IApplicationContext _context;
    private readonly IEva_GoalProgressHistoryService _service;

    public CreateEvaGoalProgressHistoryCommandHandler(IEva_GoalProgressHistoryService service, IApplicationContext context)
        : base(context.Accessor)
    {
        (_context, _service) = (context, service);
    }

    public override async Task<IApiResult<Guid>> Handle(CreateEvaGoalProgressHistoryCommand request,
        CancellationToken cancellationToken)
    {
        var entity = _context.Mapper.Map<Eva_GoalProgressHistory>(request.Request);
        var result = await _service.CreateAsync(entity);
        return Result(result);
    }
}
