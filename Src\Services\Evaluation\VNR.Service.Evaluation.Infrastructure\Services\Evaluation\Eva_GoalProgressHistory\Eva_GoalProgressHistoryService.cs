using Microsoft.EntityFrameworkCore;
using VNR.Core.Domain.Entities.Evaluation;
using VNR.Core.Domain.Repository;
using VNR.Core.Common.Logging;
using VNR.Service.Evaluation.Application.Evaluation.Eva_GoalProgressHistory.Services;

namespace VNR.Service.Evaluation.Infrastructure.Services.Evaluation;

public class Eva_GoalProgressHistoryService : IEva_GoalProgressHistoryService
{
    private readonly IGenericRepository<Eva_GoalProgressHistory, Guid> _repository;
    private readonly ILoggingService _loggingService;

    public Eva_GoalProgressHistoryService(IGenericRepository<Eva_GoalProgressHistory, Guid> repository, ILoggingService loggingService)
    {
        _repository = repository;
        _loggingService = loggingService;
    }

    public async Task<IEnumerable<Eva_GoalProgressHistory>> GetAllAsync()
    {
        await _loggingService.LogInformationAsync("Getting all Eva_GoalProgressHistory entities");
        var query = await _repository.GetAllAsync();
        return await query.ToListAsync();
    }

    public async Task<Eva_GoalProgressHistory?> GetByIdAsync(Guid id)
    {
        await _loggingService.LogInformationAsync($"Getting Eva_GoalProgressHistory by id {id}");
        return await _repository.GetByIdAsync(id);
    }

    public async Task<Guid> CreateAsync(Eva_GoalProgressHistory entity)
    {
        await _repository.AddAsync(entity);
        await _loggingService.LogInformationAsync($"Created Eva_GoalProgressHistory with id {entity.Id}");
        return entity.Id;
    }

    public async Task<bool> UpdateAsync(Eva_GoalProgressHistory entity)
    {
        await _repository.UpdateAsync(entity);
        await _loggingService.LogInformationAsync($"Updated Eva_GoalProgressHistory with id {entity.Id}");
        return true;
    }

    public async Task<bool> DeleteAsync(Guid id)
    {
        var result = await _repository.DeleteAsync(id);
        await _loggingService.LogInformationAsync($"Deleted Eva_GoalProgressHistory with id {id}");
        return result;
    }
}
