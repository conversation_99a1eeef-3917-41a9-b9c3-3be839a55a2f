using VNR.Core.Application.Command;
using VNR.Core.Domain.Entities.Evaluation;
using VNR.Core.Models;
using VNR.Infrastructure.BaseRepositories.Services.ApplicationContext;
using VNR.Service.Evaluation.Application.Evaluation.Eva_GoalLog.Services;

namespace VNR.Service.Evaluation.Application.Evaluation.Eva_GoalLog.Commands;

public class CreateEvaGoalLogCommandHandler : CommandHandler<CreateEvaGoalLogCommand, Guid>
{
    private readonly IApplicationContext _context;
    private readonly IEva_GoalLogService _service;

    public CreateEvaGoalLogCommandHandler(IEva_GoalLogService service, IApplicationContext context)
        : base(context.Accessor)
    {
        (_context, _service) = (context, service);
    }

    public override async Task<IApiResult<Guid>> Handle(CreateEvaGoalLogCommand request,
        CancellationToken cancellationToken)
    {
        var entity = _context.Mapper.Map<Eva_GoalLog>(request.Request);
        var result = await _service.CreateAsync(entity);
        return Result(result);
    }
}
