using VNR.Core.Application.Query;
using VNR.Core.Models;
using VNR.Infrastructure.BaseRepositories.Services.ApplicationContext;
using VNR.Service.Evaluation.Application.Evaluation.Services;
using VNR.Service.Evaluation.Models.Evaluation.Eva_GoalResultDetail.ResponseDtos;

namespace VNR.Service.Evaluation.Application.Evaluation.Eva_GoalResultDetail.Queries;

public class GetEvaGoalResultDetailByIdQueryHandler : QueryHandler<GetEvaGoalResultDetailByIdQuery, EvaGoalResultDetailDto>
{
    private readonly IApplicationContext _context;
    private readonly IEva_GoalResultDetailService _service;

    public GetEvaGoalResultDetailByIdQueryHandler(IEva_GoalResultDetailService service, IApplicationContext context)
        : base(context.Accessor)
    {
        (_context, _service) = (context, service);
    }

    public override async Task<IApiResult<EvaGoalResultDetailDto>> Handle(GetEvaGoalResultDetailByIdQuery request,
        CancellationToken cancellationToken)
    {
        await _context.LoggingService.LogInformationAsync($"Getting Eva_GoalResultDetail by ID: {request.ID}");

        var entity = await _service.GetByIdAsync(request.ID);

        if (entity != null)
        {
            await _context.LoggingService.LogInformationAsync($"Successfully retrieved Eva_GoalResultDetail: {request.ID}");
        }
        else
        {
            await _context.LoggingService.LogWarningAsync($"Eva_GoalResultDetail not found: {request.ID}");
        }

        var result = _context.Mapper.Map<EvaGoalResultDetailDto>(entity);
        return Result(result);
    }
}