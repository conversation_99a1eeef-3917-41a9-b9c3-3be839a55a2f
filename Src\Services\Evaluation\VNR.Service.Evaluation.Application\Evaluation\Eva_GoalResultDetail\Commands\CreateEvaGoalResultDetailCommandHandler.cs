using VNR.Core.Application.Command;
using VNR.Core.Models;
using VNR.Infrastructure.BaseRepositories.Services.ApplicationContext;
using VNR.Service.Evaluation.Application.Evaluation.Services;

namespace VNR.Service.Evaluation.Application.Evaluation.Eva_GoalResultDetail.Commands;

public class CreateEvaGoalResultDetailCommandHandler : CommandHandler<CreateEvaGoalResultDetailCommand, Guid>
{
    private readonly IApplicationContext _context;
    private readonly IEva_GoalResultDetailService _service;

    public CreateEvaGoalResultDetailCommandHandler(IEva_GoalResultDetailService service, IApplicationContext context)
        : base(context.Accessor)
    {
        (_context, _service) = (context, service);
    }

    public override async Task<IApiResult<Guid>> Handle(CreateEvaGoalResultDetailCommand request,
        CancellationToken cancellationToken)
    {
        var entity = _context.Mapper.Map<Core.Domain.Entities.Evaluation.Eva_GoalResultDetail>(request.Request);
        var id = await _service.CreateAsync(entity);
        return Result(id);
    }
}