using VNR.Core.Common.Logging;
using VNR.Core.Domain.Entities.Evaluation;
using VNR.Core.Domain.Repository;
using VNR.Service.Evaluation.Application.Evaluation.Eva_Checkin.Services;
using Microsoft.EntityFrameworkCore;

namespace VNR.Service.Evaluation.Infrastructure.Services.Evaluation.Eva_Checkin
{
    public class Eva_CheckinService : IEva_CheckinService
    {
        private readonly IGenericRepository<Eva_Checkin, Guid> _repository;
        private readonly ILoggingService _loggingService;

        public Eva_CheckinService(IGenericRepository<Eva_Checkin, Guid> repository, ILoggingService loggingService)
        {
            _repository = repository;
            _loggingService = loggingService;
        }

        public async Task<IEnumerable<Eva_Checkin>> GetAllAsync()
        {
            await _loggingService.LogInformationAsync("Getting all Eva_Checkin entities");
            var query = await _repository.GetAllAsync();
            return await query.ToListAsync();
        }

        public async Task<Eva_Checkin?> GetByIdAsync(Guid id)
        {
            await _loggingService.LogInformationAsync($"Getting Eva_Checkin by id {id}");
            return await _repository.GetByIdAsync(id);
        }

        public async Task<Guid> CreateAsync(Eva_Checkin entity)
        {
            await _repository.AddAsync(entity);
            return entity.Id;
        }

        public async Task<bool> UpdateAsync(Eva_Checkin entity)
        {
            await _repository.UpdateAsync(entity);
            return true;
        }

        public async Task<bool> DeleteAsync(Guid id)
            => await _repository.DeleteAsync(id);
    }
} 