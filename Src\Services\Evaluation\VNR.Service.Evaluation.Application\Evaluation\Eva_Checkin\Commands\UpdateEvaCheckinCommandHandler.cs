using VNR.Core.Application.Command;
using VNR.Core.Models;
using VNR.Infrastructure.BaseRepositories.Services.ApplicationContext;
using VNR.Service.Evaluation.Application.Evaluation.Services;

namespace VNR.Service.Evaluation.Application.Evaluation.Eva_Checkin.Commands;

public class UpdateEvaCheckinCommandHandler : CommandHandler<UpdateEvaCheckinCommand, bool>
{
    private readonly IApplicationContext _applicationContext;
    private readonly IEva_CheckinService _service;

    public UpdateEvaCheckinCommandHandler(IEva_CheckinService service, IApplicationContext applicationContext)
        : base(applicationContext.Accessor)
    {
        (_applicationContext, _service) = (applicationContext, service);
    }

    public override async Task<IApiResult<bool>> Handle(UpdateEvaCheckinCommand request,
        CancellationToken cancellationToken)
    {
        var entity = await _service.GetByIdAsync(request.ID);
        if (entity == null)
        {
            await _applicationContext.LoggingService.LogWarningAsync($"Eva_Checkin not found for update: {request.ID}");
            return Result(false);
        }

        _applicationContext.Mapper.Map(request.Request, entity);
        var res = await _service.UpdateAsync(entity);
        return Result(res);
    }
}