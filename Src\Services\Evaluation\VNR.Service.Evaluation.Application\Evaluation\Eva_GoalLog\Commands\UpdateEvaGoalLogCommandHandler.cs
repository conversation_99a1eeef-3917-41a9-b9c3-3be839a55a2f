using VNR.Core.Application.Command;
using VNR.Core.Domain.Entities.Evaluation;
using VNR.Core.Models;
using VNR.Infrastructure.BaseRepositories.Services.ApplicationContext;
using VNR.Service.Evaluation.Application.Evaluation.Eva_GoalLog.Services;

namespace VNR.Service.Evaluation.Application.Evaluation.Eva_GoalLog.Commands;

public class UpdateEvaGoalLogCommandHandler : CommandHandler<UpdateEvaGoalLogCommand, bool>
{
    private readonly IApplicationContext _context;
    private readonly IEva_GoalLogService _service;

    public UpdateEvaGoalLogCommandHandler(IEva_GoalLogService service, IApplicationContext context)
        : base(context.Accessor)
    {
        (_context, _service) = (context, service);
    }

    public override async Task<IApiResult<bool>> Handle(UpdateEvaGoalLogCommand request,
        CancellationToken cancellationToken)
    {
        var existingEntity = await _service.GetByIdAsync(request.ID);
        if (existingEntity == null)
        {
            await _context.LoggingService.LogWarningAsync($"Eva_GoalLog not found for update: {request.ID}");
            return Result(false);
        }

        var entity = _context.Mapper.Map<Eva_GoalLog>(request.Request);
        entity.Id = request.ID;

        var result = await _service.UpdateAsync(entity);
        return Result(result);
    }
}
