using VNR.Core.Application.Query;
using VNR.Core.Models;
using VNR.Core.Models.Base;
using VNR.Infrastructure.BaseRepositories.Services.ApplicationContext;
using VNR.Service.Evaluation.Application.Evaluation.Services;
using VNR.Service.Evaluation.Models.Evaluation.Eva_Checkin.ResponseDtos;

namespace VNR.Service.Evaluation.Application.Evaluation.Eva_Checkin.Queries;

public class ListEvaCheckinQueryHandler : QueryListHandler<ListEvaCheckinQuery, ListEvaCheckinDto>
{
    private readonly IApplicationContext _applicationContext;
    private readonly IEva_CheckinService _service;

    public ListEvaCheckinQueryHandler(IEva_CheckinService service, IApplicationContext applicationContext)
        : base(applicationContext.Accessor)
    {
        (_applicationContext, _service) = (applicationContext, service);
    }

    public override async Task<IApiResult<BaseResponseGridModel<ListEvaCheckinDto>>> Handle(ListEvaCheckinQuery query,
        CancellationToken cancellationToken)
    {
        var result = await _service.GetAllAsync();
        var finalResult = _applicationContext.Mapper.Map<IEnumerable<ListEvaCheckinDto>>(result);
        await _applicationContext.LoggingService.LogInformationAsync($"Retrieved {result.Count()} Eva_Checkin records");
        return ResultKendoGrid(query.Request, finalResult);
    }
}