using VNR.Core.Application.Query;
using VNR.Core.Models;
using VNR.Infrastructure.BaseRepositories.Services.ApplicationContext;
using VNR.Service.Evaluation.Application.Evaluation.Services;
using VNR.Service.Evaluation.Models.Evaluation.Eva_Checkin.ResponseDtos;

namespace VNR.Service.Evaluation.Application.Evaluation.Eva_Checkin.Queries;

public class GetEvaCheckinByIdQueryHandler : QueryHandler<GetEvaCheckinByIdQuery, EvaCheckinDto>
{
    private readonly IApplicationContext _applicationContext;
    private readonly IEva_CheckinService _service;

    public GetEvaCheckinByIdQueryHandler(IEva_CheckinService service, IApplicationContext applicationContext)
        : base(applicationContext.Accessor)
    {
        (_applicationContext, _service) = (applicationContext, service);
    }

    public override async Task<IApiResult<EvaCheckinDto>> Handle(GetEvaCheckinByIdQuery request,
        CancellationToken cancellationToken)
    {
        var entity = await _service.GetByIdAsync(request.ID);
        var dto = _applicationContext.Mapper.Map<EvaCheckinDto>(entity);
        return Result(dto);
    }
}