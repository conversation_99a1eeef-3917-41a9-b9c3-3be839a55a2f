using VNR.Core.Domain.Entities.Evaluation;

namespace VNR.Service.Evaluation.Application.Evaluation.Eva_CheckinSchedule.Services;
public interface IEva_CheckinScheduleService
{
    Task<IEnumerable<Eva_CheckinSchedule>> GetAllAsync();
    Task<Eva_CheckinSchedule?> GetByIdAsync(Guid id);
    Task<Guid> CreateAsync(Eva_CheckinSchedule entity);
    Task<bool> UpdateAsync(Eva_CheckinSchedule entity);
    Task<bool> DeleteAsync(Guid id);
}