using VNR.Core.Application.Command;
using VNR.Core.Domain.Entities.Evaluation;
using VNR.Core.Models;
using VNR.Infrastructure.BaseRepositories.Services.ApplicationContext;
using VNR.Service.Evaluation.Application.Evaluation.Eva_GoalResult.Services;

namespace VNR.Service.Evaluation.Application.Evaluation.Eva_GoalResult.Commands;

public class CreateEvaGoalResultCommandHandler : CommandHandler<CreateEvaGoalResultCommand, Guid>
{
    private readonly IApplicationContext _context;
    private readonly IEva_GoalResultService _service;

    public CreateEvaGoalResultCommandHandler(IEva_GoalResultService service, IApplicationContext context)
        : base(context.Accessor)
    {
        (_context, _service) = (context, service);
    }

    public override async Task<IApiResult<Guid>> Handle(CreateEvaGoalResultCommand request,
        CancellationToken cancellationToken)
    {
        var entity = _context.Mapper.Map<Eva_GoalResult>(request.Request);
        var result = await _service.CreateAsync(entity);
        return Result(result);
    }
}
