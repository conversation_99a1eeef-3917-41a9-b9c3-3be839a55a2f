using VNR.Core.Domain.Entities.Evaluation.Eva_CheckinGoal;

namespace VNR.Service.Evaluation.Application.Evaluation.Eva_CheckinGoal.Services
{
    public interface IEva_CheckinGoalService
    {
        Task<IEnumerable<Eva_CheckinGoal>> GetAllAsync();
        Task<Eva_CheckinGoal?> GetByIdAsync(Guid id);
        Task<Guid> CreateAsync(Eva_CheckinGoal entity);
        Task<bool> UpdateAsync(Eva_CheckinGoal entity);
        Task<bool> DeleteAsync(Guid id);
    }
}