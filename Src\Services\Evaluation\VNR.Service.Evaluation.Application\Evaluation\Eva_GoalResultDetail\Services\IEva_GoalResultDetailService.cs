using VNR.Core.Domain.Entities.Evaluation;

namespace VNR.Service.Evaluation.Application.Evaluation.Services
{
    public interface IEva_GoalResultDetailService
    {
        Task<IEnumerable<Eva_GoalResultDetail>> GetAllAsync();
        Task<Eva_GoalResultDetail?> GetByIdAsync(Guid id);
        Task<Guid> CreateAsync(Eva_GoalResultDetail entity);
        Task<bool> UpdateAsync(Eva_GoalResultDetail entity);
        Task<bool> DeleteAsync(Guid id);
    }
}