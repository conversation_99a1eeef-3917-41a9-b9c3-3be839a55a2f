<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AnalysisUIOptions">
    <option name="SCOPE_TYPE" value="4" />
  </component>
  <component name="AutoGeneratedRunConfigurationManager">
    <projectFile profileName="IIS Express">Src/Monitoring/VNR.Monitoring.HealthChecks.UI/VNR.Monitoring.HealthChecks.UI.csproj</projectFile>
    <projectFile profileName="http">Src/Monitoring/VNR.Monitoring.HealthChecks.UI/VNR.Monitoring.HealthChecks.UI.csproj</projectFile>
    <projectFile profileName="https">Src/Monitoring/VNR.Monitoring.HealthChecks.UI/VNR.Monitoring.HealthChecks.UI.csproj</projectFile>
    <projectFile pubXmlPath="Src/Monitoring/VNR.Monitoring.HealthChecks.UI/Properties/PublishProfiles/FolderProfile.pubxml">Src/Monitoring/VNR.Monitoring.HealthChecks.UI/VNR.Monitoring.HealthChecks.UI.csproj</projectFile>
    <projectFile>Src/Services/ConsoleApp1/ConsoleApp1.csproj</projectFile>
    <projectFile profileName="EVA">Src/Services/EVA/EVA.csproj</projectFile>
    <projectFile profileName="Development">Src/Services/Evaluation/VNR.Service.Evaluation.Api/VNR.Service.Evaluation.Api.csproj</projectFile>
    <projectFile profileName="IIS">Src/Services/Evaluation/VNR.Service.Evaluation.Api/VNR.Service.Evaluation.Api.csproj</projectFile>
    <projectFile profileName="Production">Src/Services/Evaluation/VNR.Service.Evaluation.Api/VNR.Service.Evaluation.Api.csproj</projectFile>
    <projectFile profileName="Staging">Src/Services/Evaluation/VNR.Service.Evaluation.Api/VNR.Service.Evaluation.Api.csproj</projectFile>
    <projectFile profileName="https">Src/Services/Evaluation/VNR.Service.Evaluation.Api/VNR.Service.Evaluation.Api.csproj</projectFile>
    <projectFile pubXmlPath="Src/Services/Evaluation/VNR.Service.Evaluation.Api/Properties/PublishProfiles/FolderProfile.pubxml">Src/Services/Evaluation/VNR.Service.Evaluation.Api/VNR.Service.Evaluation.Api.csproj</projectFile>
    <projectFile kind="Docker">Src/Services/IdentityServer/VNR.Service.IdentityServer.Identity/VNR.Service.IdentityServer.Identity.csproj</projectFile>
    <projectFile profileName="IIS Express">Src/Services/IdentityServer/VNR.Service.IdentityServer.Identity/VNR.Service.IdentityServer.Identity.csproj</projectFile>
    <projectFile profileName="https">Src/Services/IdentityServer/VNR.Service.IdentityServer.Identity/VNR.Service.IdentityServer.Identity.csproj</projectFile>
    <projectFile profileName="Development">Src/Services/Sample/VNR.Service.Sample.Api/VNR.Service.Sample.Api.csproj</projectFile>
    <projectFile profileName="IIS">Src/Services/Sample/VNR.Service.Sample.Api/VNR.Service.Sample.Api.csproj</projectFile>
    <projectFile profileName="Production">Src/Services/Sample/VNR.Service.Sample.Api/VNR.Service.Sample.Api.csproj</projectFile>
    <projectFile profileName="Staging">Src/Services/Sample/VNR.Service.Sample.Api/VNR.Service.Sample.Api.csproj</projectFile>
    <projectFile profileName="https">Src/Services/Sample/VNR.Service.Sample.Api/VNR.Service.Sample.Api.csproj</projectFile>
    <projectFile pubXmlPath="Src/Services/Sample/VNR.Service.Sample.Api/Properties/PublishProfiles/FolderProfile.pubxml">Src/Services/Sample/VNR.Service.Sample.Api/VNR.Service.Sample.Api.csproj</projectFile>
    <projectFile profileName="VNR.Service.Sample.Worker">Src/Services/Sample/VNR.Service.Sample.Worker/VNR.Service.Sample.Worker.csproj</projectFile>
    <projectFile profileName="Development">Src/Services/Shared/VNR.Service.Shared.Api/VNR.Service.Shared.Api.csproj</projectFile>
    <projectFile profileName="IIS">Src/Services/Shared/VNR.Service.Shared.Api/VNR.Service.Shared.Api.csproj</projectFile>
    <projectFile profileName="Production">Src/Services/Shared/VNR.Service.Shared.Api/VNR.Service.Shared.Api.csproj</projectFile>
    <projectFile profileName="Staging">Src/Services/Shared/VNR.Service.Shared.Api/VNR.Service.Shared.Api.csproj</projectFile>
    <projectFile profileName="https">Src/Services/Shared/VNR.Service.Shared.Api/VNR.Service.Shared.Api.csproj</projectFile>
    <projectFile pubXmlPath="Src/Services/Shared/VNR.Service.Shared.Api/Properties/PublishProfiles/FolderProfile.pubxml">Src/Services/Shared/VNR.Service.Shared.Api/VNR.Service.Shared.Api.csproj</projectFile>
  </component>
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="9da46500-b8c5-447b-b038-3cf2c81591d9" name="Changes" comment="">
      <change beforePath="$PROJECT_DIR$/Config/Connections.json" beforeDir="false" afterPath="$PROJECT_DIR$/Config/Connections.json" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Src/Services/Evaluation/VNR.Service.Evaluation.Api/Controllers/Category/Cat_GoalGroupController.cs" beforeDir="false" afterPath="$PROJECT_DIR$/Src/Services/Evaluation/VNR.Service.Evaluation.Api/Controllers/Category/Cat_GoalGroupController.cs" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Src/Services/Evaluation/VNR.Service.Evaluation.Api/Properties/launchSettings.json" beforeDir="false" afterPath="$PROJECT_DIR$/Src/Services/Evaluation/VNR.Service.Evaluation.Api/Properties/launchSettings.json" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Src/Services/Evaluation/VNR.Service.Evaluation.Application/Category/Cat_GoalGroup/AutoMapperProfile.cs" beforeDir="false" afterPath="$PROJECT_DIR$/Src/Services/Evaluation/VNR.Service.Evaluation.Application/Category/Cat_GoalGroup/AutoMapperProfile.cs" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Src/Services/Evaluation/VNR.Service.Evaluation.Application/Category/Cat_GoalGroup/Commands/CreateGoalGroupCommand.cs" beforeDir="false" afterPath="$PROJECT_DIR$/Src/Services/Evaluation/VNR.Service.Evaluation.Application/Category/Cat_GoalGroup/Commands/CreateGoalGroupCommand.cs" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Src/Services/Evaluation/VNR.Service.Evaluation.Application/Category/Cat_GoalGroup/Commands/CreateGoalGroupCommandHandler.cs" beforeDir="false" afterPath="$PROJECT_DIR$/Src/Services/Evaluation/VNR.Service.Evaluation.Application/Category/Cat_GoalGroup/Commands/CreateGoalGroupCommandHandler.cs" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Src/Services/Evaluation/VNR.Service.Evaluation.Application/Category/Cat_GoalGroup/Queries/ListGoalGroupQuery.cs" beforeDir="false" afterPath="$PROJECT_DIR$/Src/Services/Evaluation/VNR.Service.Evaluation.Application/Category/Cat_GoalGroup/Queries/ListGoalGroupQuery.cs" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Src/Services/Evaluation/VNR.Service.Evaluation.Application/Category/Cat_GoalGroup/Queries/ListGoalGroupQueryHandler.cs" beforeDir="false" afterPath="$PROJECT_DIR$/Src/Services/Evaluation/VNR.Service.Evaluation.Application/Category/Cat_GoalGroup/Queries/ListGoalGroupQueryHandler.cs" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Src/Services/Evaluation/VNR.Service.Evaluation.Application/Category/Cat_GoalGroup/Services/ICat_GoalGroupService.cs" beforeDir="false" afterPath="$PROJECT_DIR$/Src/Services/Evaluation/VNR.Service.Evaluation.Application/Category/Cat_GoalGroup/Services/ICat_GoalGroupService.cs" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Src/Services/Evaluation/VNR.Service.Evaluation.Application/VNR.Service.Evaluation.Application.csproj" beforeDir="false" afterPath="$PROJECT_DIR$/Src/Services/Evaluation/VNR.Service.Evaluation.Application/VNR.Service.Evaluation.Application.csproj" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Src/Services/Evaluation/VNR.Service.Evaluation.Infrastructure/Services/Category/Cat_GoalGroup/Cat_GoalGroupService.cs" beforeDir="false" afterPath="$PROJECT_DIR$/Src/Services/Evaluation/VNR.Service.Evaluation.Infrastructure/Services/Category/Cat_GoalGroup/Cat_GoalGroupService.cs" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Src/Services/Evaluation/VNR.Service.Evaluation.Infrastructure/VNR.Service.Evaluation.Infrastructure.csproj" beforeDir="false" afterPath="$PROJECT_DIR$/Src/Services/Evaluation/VNR.Service.Evaluation.Infrastructure/VNR.Service.Evaluation.Infrastructure.csproj" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Src/Services/Evaluation/VNR.Service.Evaluation.Models/Category/Cat_GoalGroup/RequestDtos/CreateGoalGroupCommandRequest.cs" beforeDir="false" afterPath="$PROJECT_DIR$/Src/Services/Evaluation/VNR.Service.Evaluation.Models/Category/Cat_GoalGroup/RequestDtos/CreateGoalGroupCommandRequest.cs" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Src/Services/Sample/VNR.Service.Sample.Api/Properties/launchSettings.json" beforeDir="false" afterPath="$PROJECT_DIR$/Src/Services/Sample/VNR.Service.Sample.Api/Properties/launchSettings.json" afterDir="false" />
    </list>
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="DpaMonitoringSettings">
    <option name="firstShow" value="false" />
  </component>
  <component name="Git.Settings">
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$" />
  </component>
  <component name="HighlightingSettingsPerFile">
    <setting file="file://$PROJECT_DIR$/Src/Services/Sample/VNR.Service.Sample.Api/Startup.cs" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$APPLICATION_CONFIG_DIR$/resharper-host/SourcesCache/294078ecfce6fdb942ecfee089f09717de7a6fcfe5efd9fdb6f4f93c0fb4813/ActionMethodExecutor.cs" root0="FORCE_HIGHLIGHTING" />
  </component>
  <component name="KubernetesApiPersistence">{}</component>
  <component name="KubernetesApiProvider">{
  &quot;isMigrated&quot;: true
}</component>
  <component name="ProblemsViewState">
    <option name="selectedTabId" value="CurrentFile" />
  </component>
  <component name="ProjectColorInfo">{
  &quot;associatedIndex&quot;: 2
}</component>
  <component name="ProjectId" id="2zZYvfP2PiNVCWy2N5qZIx2qR3B" />
  <component name="ProjectLevelVcsManager">
    <ConfirmationsSetting value="2" id="Add" />
  </component>
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent"><![CDATA[{
  "keyToString": {
    ".NET Launch Settings Profile.VNR.Service.Evaluation.Api: https.executor": "Run",
    ".NET Launch Settings Profile.VNR.Service.Sample.Api: Development.executor": "Debug",
    ".NET Launch Settings Profile.VNR.Service.Sample.Api: https.executor": "Run",
    "ModuleVcsDetector.initialDetectionPerformed": "true",
    "RunOnceActivity.ShowReadmeOnStart": "true",
    "RunOnceActivity.git.unshallow": "true",
    "git-widget-placeholder": "develop",
    "ignore.virus.scanning.warn.message": "true",
    "node.js.detected.package.eslint": "true",
    "node.js.detected.package.tslint": "true",
    "node.js.selected.package.eslint": "(autodetect)",
    "node.js.selected.package.tslint": "(autodetect)",
    "nodejs_package_manager_path": "npm",
    "settings.editor.selected.configurable": "com.codeium.AppSettingsConfigurable",
    "vue.rearranger.settings.migration": "true"
  }
}]]></component>
  <component name="RunManager" selected=".NET Launch Settings Profile.VNR.Service.Evaluation.Api: https">
    <configuration name="VNR.Monitoring.HealthChecks.UI: FolderProfile" type="DotNetMsBuildPublish" factoryName="Publish to IIS">
      <riderPublish configuration="Release" platform="Any CPU" publish_profile="FolderProfile.pubxml" pubxml_path="$PROJECT_DIR$/Src/Monitoring/VNR.Monitoring.HealthChecks.UI/Properties/PublishProfiles/FolderProfile.pubxml" uuid_high="-9135131089149014280" uuid_low="-5336104516845647466" />
      <method v="2" />
    </configuration>
    <configuration name="VNR.Service.Evaluation.Api: FolderProfile" type="DotNetMsBuildPublish" factoryName="Publish to IIS">
      <riderPublish configuration="Release" platform="Any CPU" publish_profile="FolderProfile.pubxml" pubxml_path="$PROJECT_DIR$/Src/Services/Evaluation/VNR.Service.Evaluation.Api/Properties/PublishProfiles/FolderProfile.pubxml" uuid_high="4146933415389775360" uuid_low="-7120237377076527306" />
      <method v="2" />
    </configuration>
    <configuration name="VNR.Service.Sample.Api: FolderProfile" type="DotNetMsBuildPublish" factoryName="Publish to IIS">
      <riderPublish configuration="Release" platform="Any CPU" publish_profile="FolderProfile.pubxml" pubxml_path="$PROJECT_DIR$/Src/Services/Sample/VNR.Service.Sample.Api/Properties/PublishProfiles/FolderProfile.pubxml" uuid_high="277974804773620165" uuid_low="-8225427931241112464" />
      <method v="2" />
    </configuration>
    <configuration name="VNR.Service.Shared.Api: FolderProfile" type="DotNetMsBuildPublish" factoryName="Publish to IIS">
      <riderPublish configuration="Release" platform="Any CPU" publish_profile="FolderProfile.pubxml" pubxml_path="$PROJECT_DIR$/Src/Services/Shared/VNR.Service.Shared.Api/Properties/PublishProfiles/FolderProfile.pubxml" uuid_high="3984644045671255932" uuid_low="-5558236813148915308" />
      <method v="2" />
    </configuration>
    <configuration name="ConsoleApp1" type="DotNetProject" factoryName=".NET Project">
      <option name="EXE_PATH" value="" />
      <option name="PROGRAM_PARAMETERS" value="" />
      <option name="WORKING_DIRECTORY" value="" />
      <option name="PASS_PARENT_ENVS" value="1" />
      <option name="USE_EXTERNAL_CONSOLE" value="0" />
      <option name="ENV_FILE_PATHS" value="" />
      <option name="REDIRECT_INPUT_PATH" value="" />
      <option name="PTY_MODE" value="Auto" />
      <option name="USE_MONO" value="0" />
      <option name="RUNTIME_ARGUMENTS" value="" />
      <option name="AUTO_ATTACH_CHILDREN" value="0" />
      <option name="MIXED_MODE_DEBUG" value="0" />
      <option name="PROJECT_PATH" value="$PROJECT_DIR$/Src/Services/ConsoleApp1/ConsoleApp1.csproj" />
      <option name="PROJECT_EXE_PATH_TRACKING" value="1" />
      <option name="PROJECT_ARGUMENTS_TRACKING" value="1" />
      <option name="PROJECT_WORKING_DIRECTORY_TRACKING" value="1" />
      <option name="PROJECT_KIND" value="DotNetCore" />
      <option name="PROJECT_TFM" value="" />
      <method v="2">
        <option name="Build" />
      </method>
    </configuration>
    <configuration name="EVA" type="LaunchSettings" factoryName=".NET Launch Settings Profile">
      <option name="LAUNCH_PROFILE_PROJECT_FILE_PATH" value="$PROJECT_DIR$/Src/Services/EVA/EVA.csproj" />
      <option name="LAUNCH_PROFILE_TFM" value="net8.0" />
      <option name="LAUNCH_PROFILE_NAME" value="EVA" />
      <option name="USE_EXTERNAL_CONSOLE" value="0" />
      <option name="USE_MONO" value="0" />
      <option name="RUNTIME_ARGUMENTS" value="" />
      <option name="GENERATE_APPLICATIONHOST_CONFIG" value="1" />
      <option name="SHOW_IIS_EXPRESS_OUTPUT" value="0" />
      <option name="SEND_DEBUG_REQUEST" value="1" />
      <option name="ADDITIONAL_IIS_EXPRESS_ARGUMENTS" value="" />
      <method v="2">
        <option name="Build" />
      </method>
    </configuration>
    <configuration name="VNR.Monitoring.HealthChecks.UI: IIS Express" type="LaunchSettings" factoryName=".NET Launch Settings Profile">
      <option name="LAUNCH_PROFILE_PROJECT_FILE_PATH" value="$PROJECT_DIR$/Src/Monitoring/VNR.Monitoring.HealthChecks.UI/VNR.Monitoring.HealthChecks.UI.csproj" />
      <option name="LAUNCH_PROFILE_TFM" value="net8.0" />
      <option name="LAUNCH_PROFILE_NAME" value="IIS Express" />
      <option name="USE_EXTERNAL_CONSOLE" value="0" />
      <option name="USE_MONO" value="0" />
      <option name="RUNTIME_ARGUMENTS" value="" />
      <option name="GENERATE_APPLICATIONHOST_CONFIG" value="1" />
      <option name="SHOW_IIS_EXPRESS_OUTPUT" value="0" />
      <option name="SEND_DEBUG_REQUEST" value="1" />
      <option name="ADDITIONAL_IIS_EXPRESS_ARGUMENTS" value="" />
      <method v="2">
        <option name="Build" />
      </method>
    </configuration>
    <configuration name="VNR.Monitoring.HealthChecks.UI: http" type="LaunchSettings" factoryName=".NET Launch Settings Profile">
      <option name="LAUNCH_PROFILE_PROJECT_FILE_PATH" value="$PROJECT_DIR$/Src/Monitoring/VNR.Monitoring.HealthChecks.UI/VNR.Monitoring.HealthChecks.UI.csproj" />
      <option name="LAUNCH_PROFILE_TFM" value="net8.0" />
      <option name="LAUNCH_PROFILE_NAME" value="http" />
      <option name="USE_EXTERNAL_CONSOLE" value="0" />
      <option name="USE_MONO" value="0" />
      <option name="RUNTIME_ARGUMENTS" value="" />
      <option name="GENERATE_APPLICATIONHOST_CONFIG" value="1" />
      <option name="SHOW_IIS_EXPRESS_OUTPUT" value="0" />
      <option name="SEND_DEBUG_REQUEST" value="1" />
      <option name="ADDITIONAL_IIS_EXPRESS_ARGUMENTS" value="" />
      <method v="2">
        <option name="Build" />
      </method>
    </configuration>
    <configuration name="VNR.Monitoring.HealthChecks.UI: https" type="LaunchSettings" factoryName=".NET Launch Settings Profile">
      <option name="LAUNCH_PROFILE_PROJECT_FILE_PATH" value="$PROJECT_DIR$/Src/Monitoring/VNR.Monitoring.HealthChecks.UI/VNR.Monitoring.HealthChecks.UI.csproj" />
      <option name="LAUNCH_PROFILE_TFM" value="net8.0" />
      <option name="LAUNCH_PROFILE_NAME" value="https" />
      <option name="USE_EXTERNAL_CONSOLE" value="0" />
      <option name="USE_MONO" value="0" />
      <option name="RUNTIME_ARGUMENTS" value="" />
      <option name="GENERATE_APPLICATIONHOST_CONFIG" value="1" />
      <option name="SHOW_IIS_EXPRESS_OUTPUT" value="0" />
      <option name="SEND_DEBUG_REQUEST" value="1" />
      <option name="ADDITIONAL_IIS_EXPRESS_ARGUMENTS" value="" />
      <method v="2">
        <option name="Build" />
      </method>
    </configuration>
    <configuration name="VNR.Service.Evaluation.Api: Development" type="LaunchSettings" factoryName=".NET Launch Settings Profile">
      <option name="LAUNCH_PROFILE_PROJECT_FILE_PATH" value="$PROJECT_DIR$/Src/Services/Evaluation/VNR.Service.Evaluation.Api/VNR.Service.Evaluation.Api.csproj" />
      <option name="LAUNCH_PROFILE_TFM" value="net8.0" />
      <option name="LAUNCH_PROFILE_NAME" value="Development" />
      <option name="USE_EXTERNAL_CONSOLE" value="0" />
      <option name="USE_MONO" value="0" />
      <option name="RUNTIME_ARGUMENTS" value="" />
      <option name="GENERATE_APPLICATIONHOST_CONFIG" value="1" />
      <option name="SHOW_IIS_EXPRESS_OUTPUT" value="0" />
      <option name="SEND_DEBUG_REQUEST" value="1" />
      <option name="ADDITIONAL_IIS_EXPRESS_ARGUMENTS" value="" />
      <method v="2">
        <option name="Build" />
      </method>
    </configuration>
    <configuration name="VNR.Service.Evaluation.Api: IIS" type="LaunchSettings" factoryName=".NET Launch Settings Profile">
      <option name="LAUNCH_PROFILE_PROJECT_FILE_PATH" value="$PROJECT_DIR$/Src/Services/Evaluation/VNR.Service.Evaluation.Api/VNR.Service.Evaluation.Api.csproj" />
      <option name="LAUNCH_PROFILE_TFM" value="net8.0" />
      <option name="LAUNCH_PROFILE_NAME" value="IIS" />
      <option name="USE_EXTERNAL_CONSOLE" value="0" />
      <option name="USE_MONO" value="0" />
      <option name="RUNTIME_ARGUMENTS" value="" />
      <option name="GENERATE_APPLICATIONHOST_CONFIG" value="1" />
      <option name="SHOW_IIS_EXPRESS_OUTPUT" value="0" />
      <option name="SEND_DEBUG_REQUEST" value="1" />
      <option name="ADDITIONAL_IIS_EXPRESS_ARGUMENTS" value="" />
      <method v="2">
        <option name="Build" />
      </method>
    </configuration>
    <configuration name="VNR.Service.Evaluation.Api: Production" type="LaunchSettings" factoryName=".NET Launch Settings Profile">
      <option name="LAUNCH_PROFILE_PROJECT_FILE_PATH" value="$PROJECT_DIR$/Src/Services/Evaluation/VNR.Service.Evaluation.Api/VNR.Service.Evaluation.Api.csproj" />
      <option name="LAUNCH_PROFILE_TFM" value="net8.0" />
      <option name="LAUNCH_PROFILE_NAME" value="Production" />
      <option name="USE_EXTERNAL_CONSOLE" value="0" />
      <option name="USE_MONO" value="0" />
      <option name="RUNTIME_ARGUMENTS" value="" />
      <option name="GENERATE_APPLICATIONHOST_CONFIG" value="1" />
      <option name="SHOW_IIS_EXPRESS_OUTPUT" value="0" />
      <option name="SEND_DEBUG_REQUEST" value="1" />
      <option name="ADDITIONAL_IIS_EXPRESS_ARGUMENTS" value="" />
      <method v="2">
        <option name="Build" />
      </method>
    </configuration>
    <configuration name="VNR.Service.Evaluation.Api: Staging" type="LaunchSettings" factoryName=".NET Launch Settings Profile">
      <option name="LAUNCH_PROFILE_PROJECT_FILE_PATH" value="$PROJECT_DIR$/Src/Services/Evaluation/VNR.Service.Evaluation.Api/VNR.Service.Evaluation.Api.csproj" />
      <option name="LAUNCH_PROFILE_TFM" value="net8.0" />
      <option name="LAUNCH_PROFILE_NAME" value="Staging" />
      <option name="USE_EXTERNAL_CONSOLE" value="0" />
      <option name="USE_MONO" value="0" />
      <option name="RUNTIME_ARGUMENTS" value="" />
      <option name="GENERATE_APPLICATIONHOST_CONFIG" value="1" />
      <option name="SHOW_IIS_EXPRESS_OUTPUT" value="0" />
      <option name="SEND_DEBUG_REQUEST" value="1" />
      <option name="ADDITIONAL_IIS_EXPRESS_ARGUMENTS" value="" />
      <method v="2">
        <option name="Build" />
      </method>
    </configuration>
    <configuration name="VNR.Service.Evaluation.Api: https" type="LaunchSettings" factoryName=".NET Launch Settings Profile">
      <option name="LAUNCH_PROFILE_PROJECT_FILE_PATH" value="$PROJECT_DIR$/Src/Services/Evaluation/VNR.Service.Evaluation.Api/VNR.Service.Evaluation.Api.csproj" />
      <option name="LAUNCH_PROFILE_TFM" value="net8.0" />
      <option name="LAUNCH_PROFILE_NAME" value="https" />
      <option name="USE_EXTERNAL_CONSOLE" value="0" />
      <option name="USE_MONO" value="0" />
      <option name="RUNTIME_ARGUMENTS" value="" />
      <option name="GENERATE_APPLICATIONHOST_CONFIG" value="1" />
      <option name="SHOW_IIS_EXPRESS_OUTPUT" value="0" />
      <option name="SEND_DEBUG_REQUEST" value="1" />
      <option name="ADDITIONAL_IIS_EXPRESS_ARGUMENTS" value="" />
      <method v="2">
        <option name="Build" />
      </method>
    </configuration>
    <configuration name="VNR.Service.IdentityServer.Identity: IIS Express" type="LaunchSettings" factoryName=".NET Launch Settings Profile">
      <option name="LAUNCH_PROFILE_PROJECT_FILE_PATH" value="$PROJECT_DIR$/Src/Services/IdentityServer/VNR.Service.IdentityServer.Identity/VNR.Service.IdentityServer.Identity.csproj" />
      <option name="LAUNCH_PROFILE_TFM" value="net8.0" />
      <option name="LAUNCH_PROFILE_NAME" value="IIS Express" />
      <option name="USE_EXTERNAL_CONSOLE" value="0" />
      <option name="USE_MONO" value="0" />
      <option name="RUNTIME_ARGUMENTS" value="" />
      <option name="GENERATE_APPLICATIONHOST_CONFIG" value="1" />
      <option name="SHOW_IIS_EXPRESS_OUTPUT" value="0" />
      <option name="SEND_DEBUG_REQUEST" value="1" />
      <option name="ADDITIONAL_IIS_EXPRESS_ARGUMENTS" value="" />
      <method v="2">
        <option name="Build" />
      </method>
    </configuration>
    <configuration name="VNR.Service.IdentityServer.Identity: https" type="LaunchSettings" factoryName=".NET Launch Settings Profile">
      <option name="LAUNCH_PROFILE_PROJECT_FILE_PATH" value="$PROJECT_DIR$/Src/Services/IdentityServer/VNR.Service.IdentityServer.Identity/VNR.Service.IdentityServer.Identity.csproj" />
      <option name="LAUNCH_PROFILE_TFM" value="net8.0" />
      <option name="LAUNCH_PROFILE_NAME" value="https" />
      <option name="USE_EXTERNAL_CONSOLE" value="0" />
      <option name="USE_MONO" value="0" />
      <option name="RUNTIME_ARGUMENTS" value="" />
      <option name="GENERATE_APPLICATIONHOST_CONFIG" value="1" />
      <option name="SHOW_IIS_EXPRESS_OUTPUT" value="0" />
      <option name="SEND_DEBUG_REQUEST" value="1" />
      <option name="ADDITIONAL_IIS_EXPRESS_ARGUMENTS" value="" />
      <method v="2">
        <option name="Build" />
      </method>
    </configuration>
    <configuration name="VNR.Service.Sample.Api: Development" type="LaunchSettings" factoryName=".NET Launch Settings Profile">
      <option name="LAUNCH_PROFILE_PROJECT_FILE_PATH" value="$PROJECT_DIR$/Src/Services/Sample/VNR.Service.Sample.Api/VNR.Service.Sample.Api.csproj" />
      <option name="LAUNCH_PROFILE_TFM" value="net8.0" />
      <option name="LAUNCH_PROFILE_NAME" value="Development" />
      <option name="USE_EXTERNAL_CONSOLE" value="0" />
      <option name="USE_MONO" value="0" />
      <option name="RUNTIME_ARGUMENTS" value="" />
      <option name="GENERATE_APPLICATIONHOST_CONFIG" value="1" />
      <option name="SHOW_IIS_EXPRESS_OUTPUT" value="0" />
      <option name="SEND_DEBUG_REQUEST" value="1" />
      <option name="ADDITIONAL_IIS_EXPRESS_ARGUMENTS" value="" />
      <method v="2">
        <option name="Build" />
      </method>
    </configuration>
    <configuration name="VNR.Service.Sample.Api: Production" type="LaunchSettings" factoryName=".NET Launch Settings Profile">
      <option name="LAUNCH_PROFILE_PROJECT_FILE_PATH" value="$PROJECT_DIR$/Src/Services/Sample/VNR.Service.Sample.Api/VNR.Service.Sample.Api.csproj" />
      <option name="LAUNCH_PROFILE_TFM" value="net8.0" />
      <option name="LAUNCH_PROFILE_NAME" value="Production" />
      <option name="USE_EXTERNAL_CONSOLE" value="0" />
      <option name="USE_MONO" value="0" />
      <option name="RUNTIME_ARGUMENTS" value="" />
      <option name="GENERATE_APPLICATIONHOST_CONFIG" value="1" />
      <option name="SHOW_IIS_EXPRESS_OUTPUT" value="0" />
      <option name="SEND_DEBUG_REQUEST" value="1" />
      <option name="ADDITIONAL_IIS_EXPRESS_ARGUMENTS" value="" />
      <method v="2">
        <option name="Build" />
      </method>
    </configuration>
    <configuration name="VNR.Service.Sample.Api: Staging" type="LaunchSettings" factoryName=".NET Launch Settings Profile">
      <option name="LAUNCH_PROFILE_PROJECT_FILE_PATH" value="$PROJECT_DIR$/Src/Services/Sample/VNR.Service.Sample.Api/VNR.Service.Sample.Api.csproj" />
      <option name="LAUNCH_PROFILE_TFM" value="net8.0" />
      <option name="LAUNCH_PROFILE_NAME" value="Staging" />
      <option name="USE_EXTERNAL_CONSOLE" value="0" />
      <option name="USE_MONO" value="0" />
      <option name="RUNTIME_ARGUMENTS" value="" />
      <option name="GENERATE_APPLICATIONHOST_CONFIG" value="1" />
      <option name="SHOW_IIS_EXPRESS_OUTPUT" value="0" />
      <option name="SEND_DEBUG_REQUEST" value="1" />
      <option name="ADDITIONAL_IIS_EXPRESS_ARGUMENTS" value="" />
      <method v="2">
        <option name="Build" />
      </method>
    </configuration>
    <configuration name="VNR.Service.Sample.Api: https" type="LaunchSettings" factoryName=".NET Launch Settings Profile">
      <option name="LAUNCH_PROFILE_PROJECT_FILE_PATH" value="$PROJECT_DIR$/Src/Services/Sample/VNR.Service.Sample.Api/VNR.Service.Sample.Api.csproj" />
      <option name="LAUNCH_PROFILE_TFM" value="net8.0" />
      <option name="LAUNCH_PROFILE_NAME" value="https" />
      <option name="USE_EXTERNAL_CONSOLE" value="0" />
      <option name="USE_MONO" value="0" />
      <option name="RUNTIME_ARGUMENTS" value="" />
      <option name="GENERATE_APPLICATIONHOST_CONFIG" value="1" />
      <option name="SHOW_IIS_EXPRESS_OUTPUT" value="0" />
      <option name="SEND_DEBUG_REQUEST" value="1" />
      <option name="ADDITIONAL_IIS_EXPRESS_ARGUMENTS" value="" />
      <method v="2">
        <option name="Build" />
      </method>
    </configuration>
    <configuration name="VNR.Service.Sample.Worker" type="LaunchSettings" factoryName=".NET Launch Settings Profile">
      <option name="LAUNCH_PROFILE_PROJECT_FILE_PATH" value="$PROJECT_DIR$/Src/Services/Sample/VNR.Service.Sample.Worker/VNR.Service.Sample.Worker.csproj" />
      <option name="LAUNCH_PROFILE_TFM" value="net8.0" />
      <option name="LAUNCH_PROFILE_NAME" value="VNR.Service.Sample.Worker" />
      <option name="USE_EXTERNAL_CONSOLE" value="0" />
      <option name="USE_MONO" value="0" />
      <option name="RUNTIME_ARGUMENTS" value="" />
      <option name="GENERATE_APPLICATIONHOST_CONFIG" value="1" />
      <option name="SHOW_IIS_EXPRESS_OUTPUT" value="0" />
      <option name="SEND_DEBUG_REQUEST" value="1" />
      <option name="ADDITIONAL_IIS_EXPRESS_ARGUMENTS" value="" />
      <method v="2">
        <option name="Build" />
      </method>
    </configuration>
    <configuration name="VNR.Service.Shared.Api: Development" type="LaunchSettings" factoryName=".NET Launch Settings Profile">
      <option name="LAUNCH_PROFILE_PROJECT_FILE_PATH" value="$PROJECT_DIR$/Src/Services/Shared/VNR.Service.Shared.Api/VNR.Service.Shared.Api.csproj" />
      <option name="LAUNCH_PROFILE_TFM" value="net8.0" />
      <option name="LAUNCH_PROFILE_NAME" value="Development" />
      <option name="USE_EXTERNAL_CONSOLE" value="0" />
      <option name="USE_MONO" value="0" />
      <option name="RUNTIME_ARGUMENTS" value="" />
      <option name="GENERATE_APPLICATIONHOST_CONFIG" value="1" />
      <option name="SHOW_IIS_EXPRESS_OUTPUT" value="0" />
      <option name="SEND_DEBUG_REQUEST" value="1" />
      <option name="ADDITIONAL_IIS_EXPRESS_ARGUMENTS" value="" />
      <method v="2">
        <option name="Build" />
      </method>
    </configuration>
    <configuration name="VNR.Service.Shared.Api: IIS" type="LaunchSettings" factoryName=".NET Launch Settings Profile">
      <option name="LAUNCH_PROFILE_PROJECT_FILE_PATH" value="$PROJECT_DIR$/Src/Services/Shared/VNR.Service.Shared.Api/VNR.Service.Shared.Api.csproj" />
      <option name="LAUNCH_PROFILE_TFM" value="net8.0" />
      <option name="LAUNCH_PROFILE_NAME" value="IIS" />
      <option name="USE_EXTERNAL_CONSOLE" value="0" />
      <option name="USE_MONO" value="0" />
      <option name="RUNTIME_ARGUMENTS" value="" />
      <option name="GENERATE_APPLICATIONHOST_CONFIG" value="1" />
      <option name="SHOW_IIS_EXPRESS_OUTPUT" value="0" />
      <option name="SEND_DEBUG_REQUEST" value="1" />
      <option name="ADDITIONAL_IIS_EXPRESS_ARGUMENTS" value="" />
      <method v="2">
        <option name="Build" />
      </method>
    </configuration>
    <configuration name="VNR.Service.Shared.Api: Production" type="LaunchSettings" factoryName=".NET Launch Settings Profile">
      <option name="LAUNCH_PROFILE_PROJECT_FILE_PATH" value="$PROJECT_DIR$/Src/Services/Shared/VNR.Service.Shared.Api/VNR.Service.Shared.Api.csproj" />
      <option name="LAUNCH_PROFILE_TFM" value="net8.0" />
      <option name="LAUNCH_PROFILE_NAME" value="Production" />
      <option name="USE_EXTERNAL_CONSOLE" value="0" />
      <option name="USE_MONO" value="0" />
      <option name="RUNTIME_ARGUMENTS" value="" />
      <option name="GENERATE_APPLICATIONHOST_CONFIG" value="1" />
      <option name="SHOW_IIS_EXPRESS_OUTPUT" value="0" />
      <option name="SEND_DEBUG_REQUEST" value="1" />
      <option name="ADDITIONAL_IIS_EXPRESS_ARGUMENTS" value="" />
      <method v="2">
        <option name="Build" />
      </method>
    </configuration>
    <configuration name="VNR.Service.Shared.Api: Staging" type="LaunchSettings" factoryName=".NET Launch Settings Profile">
      <option name="LAUNCH_PROFILE_PROJECT_FILE_PATH" value="$PROJECT_DIR$/Src/Services/Shared/VNR.Service.Shared.Api/VNR.Service.Shared.Api.csproj" />
      <option name="LAUNCH_PROFILE_TFM" value="net8.0" />
      <option name="LAUNCH_PROFILE_NAME" value="Staging" />
      <option name="USE_EXTERNAL_CONSOLE" value="0" />
      <option name="USE_MONO" value="0" />
      <option name="RUNTIME_ARGUMENTS" value="" />
      <option name="GENERATE_APPLICATIONHOST_CONFIG" value="1" />
      <option name="SHOW_IIS_EXPRESS_OUTPUT" value="0" />
      <option name="SEND_DEBUG_REQUEST" value="1" />
      <option name="ADDITIONAL_IIS_EXPRESS_ARGUMENTS" value="" />
      <method v="2">
        <option name="Build" />
      </method>
    </configuration>
    <configuration name="VNR.Service.Shared.Api: https" type="LaunchSettings" factoryName=".NET Launch Settings Profile">
      <option name="LAUNCH_PROFILE_PROJECT_FILE_PATH" value="$PROJECT_DIR$/Src/Services/Shared/VNR.Service.Shared.Api/VNR.Service.Shared.Api.csproj" />
      <option name="LAUNCH_PROFILE_TFM" value="net8.0" />
      <option name="LAUNCH_PROFILE_NAME" value="https" />
      <option name="USE_EXTERNAL_CONSOLE" value="0" />
      <option name="USE_MONO" value="0" />
      <option name="RUNTIME_ARGUMENTS" value="" />
      <option name="GENERATE_APPLICATIONHOST_CONFIG" value="1" />
      <option name="SHOW_IIS_EXPRESS_OUTPUT" value="0" />
      <option name="SEND_DEBUG_REQUEST" value="1" />
      <option name="ADDITIONAL_IIS_EXPRESS_ARGUMENTS" value="" />
      <method v="2">
        <option name="Build" />
      </method>
    </configuration>
    <configuration name="VNR.Service.IdentityServer.Identity/Dockerfile" type="docker-deploy" factoryName="dockerfile" server-name="Docker">
      <deployment type="dockerfile">
        <settings>
          <option name="containerName" value="vnr.service.identityserver.identity" />
          <option name="contextFolderPath" value="C:\SandboxShare\VNR.Solution\Src\Services" />
          <option name="sourceFilePath" value="Src/Services/IdentityServer/VNR.Service.IdentityServer.Identity/Dockerfile" />
        </settings>
      </deployment>
      <EXTENSION ID="com.jetbrains.rider.docker.debug" isFastModeEnabled="true" isSslEnabled="false" />
      <method v="2" />
    </configuration>
    <configuration default="true" type="docker-deploy" factoryName="dockerfile" temporary="true">
      <deployment type="dockerfile">
        <settings />
      </deployment>
      <EXTENSION ID="com.jetbrains.rider.docker.debug" isFastModeEnabled="true" isSslEnabled="false" />
      <method v="2" />
    </configuration>
  </component>
  <component name="TaskManager">
    <task active="true" id="Default" summary="Default task">
      <changelist id="9da46500-b8c5-447b-b038-3cf2c81591d9" name="Changes" comment="" />
      <created>1751940295286</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1751940295286</updated>
      <workItem from="1751940299622" duration="1107000" />
      <workItem from="1751941466842" duration="183000" />
      <workItem from="1751941674261" duration="242000" />
      <workItem from="1751941965249" duration="71000" />
      <workItem from="1751942059162" duration="431000" />
      <workItem from="1751942517638" duration="303000" />
      <workItem from="1751942851753" duration="3653000" />
      <workItem from="1752053722410" duration="5051000" />
      <workItem from="1752115222073" duration="118000" />
      <workItem from="1752115361479" duration="15766000" />
      <workItem from="1752197149562" duration="28031000" />
    </task>
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
  <component name="UnityProjectConfiguration" hasMinimizedUI="false" />
  <component name="UnknownFeatures">
    <option featureType="dependencySupport" implementationName="executable:docker" />
    <option featureType="dependencySupport" implementationName="dotnet:Microsoft.Identity.Client" />
    <option featureType="dependencySupport" implementationName="executable:kubectl" />
    <option featureType="dependencySupport" implementationName="dotnet:Confluent.Kafka" />
    <option featureType="dependencySupport" implementationName="dotnet:Microsoft.EntityFrameworkCore.Abstractions" />
    <option featureType="dependencySupport" implementationName="dotnet:Microsoft.EntityFrameworkCore.Relational" />
    <option featureType="dependencySupport" implementationName="dotnet:Microsoft.EntityFrameworkCore" />
  </component>
  <component name="VcsManagerConfiguration">
    <option name="CLEAR_INITIAL_COMMIT_MESSAGE" value="true" />
  </component>
  <component name="XDebuggerManager">
    <breakpoint-manager>
      <breakpoints>
        <breakpoint enabled="true" type="DotNet_Exception_Breakpoints">
          <properties exception="System.OperationCanceledException" breakIfHandledByOtherCode="false" displayValue="System.OperationCanceledException" />
          <option name="timeStamp" value="1" />
        </breakpoint>
        <breakpoint enabled="true" type="DotNet_Exception_Breakpoints">
          <properties exception="System.Threading.Tasks.TaskCanceledException" breakIfHandledByOtherCode="false" displayValue="System.Threading.Tasks.TaskCanceledException" />
          <option name="timeStamp" value="2" />
        </breakpoint>
        <breakpoint enabled="true" type="DotNet_Exception_Breakpoints">
          <properties exception="System.Threading.ThreadAbortException" breakIfHandledByOtherCode="false" displayValue="System.Threading.ThreadAbortException" />
          <option name="timeStamp" value="3" />
        </breakpoint>
        <line-breakpoint enabled="true" type="DotNet Breakpoints">
          <url>file://$PROJECT_DIR$/Src/Services/Sample/VNR.Service.Sample.Api/Controllers/Configuration/ConfigurationTestController.cs</url>
          <line>30</line>
          <properties documentPath="C:\SandboxShare\VNR.Solution\Src\Services\Sample\VNR.Service.Sample.Api\Controllers\Configuration\ConfigurationTestController.cs" containingFunctionPresentation="Method 'CheckConfiguration'">
            <startOffsets>
              <option value="917" />
            </startOffsets>
            <endOffsets>
              <option value="998" />
            </endOffsets>
          </properties>
          <option name="timeStamp" value="5" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="DotNet Breakpoints">
          <url>file://$PROJECT_DIR$/Src/Services/Evaluation/VNR.Service.Evaluation.Api/Controllers/Category/Cat_MeasurementScaleController.cs</url>
          <line>26</line>
          <properties documentPath="C:\SandboxShare\VNR.Solution\Src\Services\Evaluation\VNR.Service.Evaluation.Api\Controllers\Category\Cat_MeasurementScaleController.cs" containingFunctionPresentation="Method 'List'">
            <startOffsets>
              <option value="977" />
            </startOffsets>
            <endOffsets>
              <option value="1005" />
            </endOffsets>
          </properties>
          <option name="timeStamp" value="14" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="DotNet Breakpoints">
          <url>file://$PROJECT_DIR$/Src/Services/Evaluation/VNR.Service.Evaluation.Infrastructure/Services/Evaluation/Eva_Goal/Eva_GoalService.cs</url>
          <line>29</line>
          <properties documentPath="C:\SandboxShare\VNR.Solution\Src\Services\Evaluation\VNR.Service.Evaluation.Infrastructure\Services\Evaluation\Eva_Goal\Eva_GoalService.cs" containingFunctionPresentation="Method 'CreateAsync'">
            <startOffsets>
              <option value="1178" />
            </startOffsets>
            <endOffsets>
              <option value="1213" />
            </endOffsets>
          </properties>
          <option name="timeStamp" value="15" />
        </line-breakpoint>
      </breakpoints>
    </breakpoint-manager>
  </component>
  <component name="XSLT-Support.FileAssociations.UIState">
    <expand />
    <select />
  </component>
</project>