using VNR.Core.Application.Command;
using VNR.Core.Models;
using VNR.Infrastructure.BaseRepositories.Services.ApplicationContext;
using VNR.Service.Evaluation.Application.Evaluation.Services;

namespace VNR.Service.Evaluation.Application.Evaluation.Eva_Checkin.Commands;

public class DeleteEvaCheckinCommandHandler : CommandHandler<DeleteEvaCheckinCommand, bool>
{
    private readonly IApplicationContext _applicationContext;
    private readonly IEva_CheckinService _service;

    public DeleteEvaCheckinCommandHandler(IEva_CheckinService service, IApplicationContext applicationContext)
        : base(applicationContext.Accessor)
    {
        (_applicationContext, _service) = (applicationContext, service);
    }

    public override async Task<IApiResult<bool>> Handle(DeleteEvaCheckinCommand request,
        CancellationToken cancellationToken)
    {
        var res = await _service.DeleteAsync(request.ID);
        return Result(res);
    }
}