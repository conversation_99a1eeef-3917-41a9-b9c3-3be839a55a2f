using VNR.Core.Common.Logging;
using VNR.Core.Domain.Entities.Evaluation;
using VNR.Core.Domain.Repository;
using VNR.Service.Evaluation.Application.Evaluation.Eva_CheckinGoal.Services;
using Microsoft.EntityFrameworkCore;

namespace VNR.Service.Evaluation.Infrastructure.Services.Evaluation.Eva_CheckinGoal
{
    public class Eva_CheckinGoalService : IEva_CheckinGoalService
    {
        private readonly IGenericRepository<Eva_CheckinGoal, Guid> _repository;
        private readonly ILoggingService _loggingService;

        public Eva_CheckinGoalService(IGenericRepository<Eva_CheckinGoal, Guid> repository, ILoggingService loggingService)
        {
            _repository = repository;
            _loggingService = loggingService;
        }

        public async Task<IEnumerable<Eva_CheckinGoal>> GetAllAsync()
        {
            await _loggingService.LogInformationAsync("Getting all Eva_CheckinGoal entities");
            var query = await _repository.GetAllAsync();
            return await query.ToListAsync();
        }

        public async Task<Eva_CheckinGoal?> GetByIdAsync(Guid id)
        {
            await _loggingService.LogInformationAsync($"Getting Eva_CheckinGoal by id {id}");
            return await _repository.GetByIdAsync(id);
        }

        public async Task<Guid> CreateAsync(Eva_CheckinGoal entity)
        {
            await _repository.AddAsync(entity);
            return entity.Id;
        }

        public async Task<bool> UpdateAsync(Eva_CheckinGoal entity)
        {
            await _repository.UpdateAsync(entity);
            return true;
        }

        public async Task<bool> DeleteAsync(Guid id)
            => await _repository.DeleteAsync(id);
    }
} 