using Microsoft.EntityFrameworkCore;
using VNR.Core.Common.Logging;
using VNR.Core.Domain.Entities.Evaluation;
using VNR.Core.Domain.Repository;
using VNR.Service.Evaluation.Application.Evaluation.Eva_GoalResultDetail.Services;

namespace VNR.Service.Evaluation.Infrastructure.Services.Evaluation.Eva_GoalResultDetail;

public class Eva_GoalResultDetailService : IEva_GoalResultDetailService
{
    private readonly IGenericRepository<Eva_GoalResultDetail, Guid> _repository;
    private readonly ILoggingService _loggingService;

    public Eva_GoalResultDetailService(IGenericRepository<Eva_GoalResultDetail, Guid> repository, ILoggingService loggingService)
    {
        _repository = repository;
        _loggingService = loggingService;
    }

    public async Task<IEnumerable<Eva_GoalResultDetail>> GetAllAsync()
    {
        await _loggingService.LogInformationAsync("Getting all Eva_GoalResultDetail entities");
        var query = await _repository.GetAllAsync();
        return await query.ToListAsync();
    }

    public async Task<Eva_GoalResultDetail?> GetByIdAsync(Guid id)
    {
        await _loggingService.LogInformationAsync($"Getting Eva_GoalResultDetail by id {id}");
        return await _repository.GetByIdAsync(id);
    }

    public async Task<Guid> CreateAsync(Eva_GoalResultDetail entity)
    {
        await _repository.AddAsync(entity);
        await _loggingService.LogInformationAsync($"Created Eva_GoalResultDetail with id {entity.Id}");
        return entity.Id;
    }

    public async Task<bool> UpdateAsync(Eva_GoalResultDetail entity)
    {
        await _repository.UpdateAsync(entity);
        await _loggingService.LogInformationAsync($"Updated Eva_GoalResultDetail with id {entity.Id}");
        return true;
    }

    public async Task<bool> DeleteAsync(Guid id)
    {
        var result = await _repository.DeleteAsync(id);
        await _loggingService.LogInformationAsync($"Deleted Eva_GoalResultDetail with id {id}");
        return result;
    }
}